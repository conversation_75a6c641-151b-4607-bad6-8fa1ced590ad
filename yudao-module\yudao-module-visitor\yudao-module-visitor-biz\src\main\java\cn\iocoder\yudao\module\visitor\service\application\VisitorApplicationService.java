package cn.iocoder.yudao.module.visitor.service.application;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationCreateReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationPageReqVO;
import cn.iocoder.yudao.module.visitor.controller.admin.application.vo.VisitorApplicationUpdateReqVO;
import cn.iocoder.yudao.module.visitor.controller.app.guard.vo.AppQrCodeScanRespVO;
import cn.iocoder.yudao.module.visitor.dal.dataobject.VisitorApplicationDO;
import jakarta.validation.Valid;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * 访客申请 Service 接口
 *
 * <AUTHOR>
 * @since 2025-08-12
 */
public interface VisitorApplicationService {

    /**
     * 创建访客申请
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createVisitorApplication(@Valid VisitorApplicationCreateReqVO createReqVO);

    /**
     * 更新访客申请
     *
     * @param updateReqVO 更新信息
     */
    void updateVisitorApplication(@Valid VisitorApplicationUpdateReqVO updateReqVO);

    /**
     * 删除访客申请
     *
     * @param id 编号
     */
    void deleteVisitorApplication(Long id);

    /**
     * 获得访客申请
     *
     * @param id 编号
     * @return 访客申请
     */
    VisitorApplicationDO getVisitorApplication(Long id);

    /**
     * 获得访客申请列表
     *
     * @param ids 编号
     * @return 访客申请列表
     */
    List<VisitorApplicationDO> getVisitorApplicationList(Collection<Long> ids);

    /**
     * 获得访客申请分页
     *
     * @param pageReqVO 分页查询
     * @return 访客申请分页
     */
    PageResult<VisitorApplicationDO> getVisitorApplicationPage(VisitorApplicationPageReqVO pageReqVO);

    /**
     * 获得访客申请列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return 访客申请列表
     */
    List<VisitorApplicationDO> getVisitorApplicationList(VisitorApplicationPageReqVO exportReqVO);

    /**
     * 鏍规嵁申请鍗曞彿获取访客申请
     *
     * @param applicationNo 申请鍗曞彿
     * @return 访客申请
     */
    VisitorApplicationDO getVisitorApplicationByNo(String applicationNo);

    /**
     * 鏍规嵁娴佺▼瀹炰緥ID获取访客申请
     *
     * @param processInstanceId 娴佺▼瀹炰緥ID
     * @return 访客申请
     */
    VisitorApplicationDO getVisitorApplicationByProcessInstanceId(String processInstanceId);

    /**
     * 提交访客申请锛堝惎鍔ㄥ鎵规祦绋嬶級
     *
     * @param id 申请ID
     * @return 娴佺▼瀹炰緥ID
     */
    String submitVisitorApplication(Long id);

    /**
     * 联系人虹‘璁よ瀹㈢敵璇?     *
     * @param id 申请ID
     * @param confirmed 鏄惁纭
     * @param reason 纭鎰忚
     */
    void confirmVisitorApplication(Long id, Boolean confirmed, String reason);

    /**
     * 瀹℃壒访客申请
     *
     * @param id 申请ID
     * @param approved 鏄惁通过
     * @param reason 瀹℃壒鎰忚
     */
    void approveVisitorApplication(Long id, Boolean approved, String reason);

    /**
     * 鐢熸垚访客二维码?     *
     * @param id 申请ID
     * @return 二维码乁RL
     */
    String generateVisitorQrCode(Long id);

    /**
     * 访客入园登记
     *
     * @param id 申请ID
     * @param operatorId 操作员業D
     * @param gateLocation 门岗浣嶇疆
     * @param verificationMethod 验证鏂瑰紡
     * @param temperature 浣撴俯
     * @param remarks 备注
     */
    void entryVisitor(Long id, Long operatorId, String gateLocation, Integer verificationMethod,
                     Double temperature, String remarks);

    /**
     * 访客出园登记
     *
     * @param id 申请ID
     * @param operatorId 操作员業D
     * @param gateLocation 门岗浣嶇疆
     * @param remarks 备注
     */
    void exitVisitor(Long id, Long operatorId, String gateLocation, String remarks);

    /**
     * 鍙栨秷访客申请
     *
     * @param id 申请ID
     * @param reason 鍙栨秷鍘熷洜
     */
    void cancelVisitorApplication(Long id, String reason);

    /**
     * 更新访客申请状态?     *
     * @param id 申请ID
     * @param status 新状态
     * @param reason 变更原因
     */
    void updateVisitorApplicationStatus(Long id, Integer status, String reason);

    /**
     * 检查查询客申请是否过期?     *
     * @param id 申请ID
     * @return 鏄惁杩囨湡
     */
    boolean isVisitorApplicationExpired(Long id);

    /**
     * 澶勭悊杩囨湡鐨勮瀹㈢敵璇?     */
    void handleExpiredApplications();

    /**
     * 鎵弿访客二维码?     *
     * @param qrCodeContent 二维码佸唴瀹?     * @return 鎵弿缁撴灉
     */
    AppQrCodeScanRespVO scanQrCode(String qrCodeContent);

    /**
     * 获取访客申请统计鏁版嵁
     *
     * @param startTime 开始嬫椂闂?     * @param endTime 结束时间
     * @return 统计鏁版嵁
     */
    Object getVisitorApplicationStatistics(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 验证访客申请鏁版嵁
     *
     * @param application 访客申请
     */
    void validateVisitorApplication(VisitorApplicationDO application);

    /**
     * 鐢熸垚申请鍗曞彿
     *
     * @return 申请鍗曞彿
     */
    String generateApplicationNo();

}


